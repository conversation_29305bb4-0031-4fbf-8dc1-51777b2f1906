import os
import json
import hashlib
import requests
from bs4 import Beautiful<PERSON>oup
from openai import OpenAI
from org_details_desc import ORG_SCHEMA_DESC
from utils import cache_path, save_json, search_google, scrape_text_from_links, build_prompt, call_llm, process_missing_fields

CACHE_DIR = "/Users/<USER>/Downloads/power_plant_project/cache/org_cache"
WORKSPACE_DIR = "/Users/<USER>/Downloads/power_plant_project/workspace/org_details"

def process_org_details(plant_name: str) -> dict:
    """Process organization details with improved error handling."""
    cache_key = f"org_{plant_name}"
    cached_path = cache_path(cache_key)

    # Check if we have cached results
    if os.path.exists(cached_path):
        print(f"📁 Loading cached organization details for {plant_name}")
        cached_data = json.load(open(cached_path))

        # Check for missing fields and process them
        print("🔍 Checking for missing fields in cached data...")
        updated_data = process_missing_fields(plant_name, cached_data, ORG_SCHEMA_DESC, cached_path)

        # Save to workspace if updated
        if updated_data != cached_data:
            os.makedirs(WORKSPACE_DIR, exist_ok=True)
            save_json(updated_data, os.path.join(WORKSPACE_DIR, f"org_details_{plant_name}.json"))

        return updated_data

    print(f"🔍 Processing organization details for {plant_name}")

    query = f"{plant_name} power plant organization details"

    try:
        links = search_google(query, num_results=5)

        if not links:
            print("⚠️ No search results found for organization details")
            return {}

        text, sources = scrape_text_from_links(links, max_content_length=12000)

        if not text.strip():
            print("⚠️ No content extracted from organization links")
            return {}

        # Limit text to avoid token issues
        text = text[:10000]

        prompt = build_prompt("org_details", ORG_SCHEMA_DESC, text)
        result = call_llm(prompt, max_tokens=2000, schema=ORG_SCHEMA_DESC, sources=sources,
                          plant_name=plant_name, field_name="organization_details", workspace_type="org")

        # Save initial results
        os.makedirs(os.path.dirname(cached_path), exist_ok=True)
        save_json(result, cached_path)

        # Process missing fields
        print("🔍 Checking for missing fields in initial extraction...")
        final_result = process_missing_fields(plant_name, result, ORG_SCHEMA_DESC, cached_path)

        # Save final results to workspace
        os.makedirs(WORKSPACE_DIR, exist_ok=True)
        save_json(final_result, os.path.join(WORKSPACE_DIR, f"org_details_{plant_name}.json"))

        print(f"✅ Successfully processed organization details for {plant_name}")
        return final_result

    except Exception as e:
        print(f"❌ Failed to process organization details: {e}")
        return {}
