#!/usr/bin/env python3
"""
Test script to verify field description-based query formation.
"""

import json
from org_details_desc import ORG_SCHEMA_DESC
from utils import smart_field_search

def test_description_based_queries():
    """Test that queries are formed using field descriptions."""
    print("🧪 Testing Description-Based Query Formation\n")
    
    plant_name = "Jhajjar Power Plant"
    
    # Test different fields with their descriptions
    test_fields = [
        ("cfpp_type", ORG_SCHEMA_DESC["cfpp_type"]),
        ("currency_in", ORG_SCHEMA_DESC["currency_in"]),
        ("financial_year", ORG_SCHEMA_DESC["financial_year"]),
        ("ppa_flag", ORG_SCHEMA_DESC["ppa_flag"])
    ]
    
    for field_name, field_description in test_fields:
        print(f"🎯 Testing field: {field_name}")
        print(f"📝 Description: {field_description}")
        
        # Expected queries:
        expected_level1 = f'"{plant_name}" "{field_description}"'
        print(f"🔍 Expected Level 1: {expected_level1}")
        
        # Test the search
        content, sources = smart_field_search(plant_name, field_name, field_description)
        
        if content:
            print(f"✅ Found content: {len(content)} chars")
            print(f"🔗 Sources: {len(sources)} URLs")
        else:
            print("❌ No content found")
        
        print("-" * 80)
        print()

def test_specific_field_searches():
    """Test specific field searches with real descriptions."""
    print("🧪 Testing Specific Field Searches\n")
    
    plant_name = "Jhajjar Power Plant"
    
    # Test currency_in with its exact description
    print("💰 Testing currency_in field:")
    field_description = "ISO 4217 currency code of that power plant country"
    print(f"📝 Description: {field_description}")
    
    content, sources = smart_field_search(plant_name, "currency_in", field_description)
    
    if content:
        print(f"✅ Currency search found {len(content)} chars of content")
        print(f"🔗 Sources: {sources}")
        
        # Look for currency-related terms in content
        currency_terms = ["INR", "rupee", "currency", "ISO 4217", "Indian"]
        found_terms = [term for term in currency_terms if term.lower() in content.lower()]
        print(f"💱 Currency terms found: {found_terms}")
    else:
        print("❌ No currency content found")
    
    print("\n" + "="*80 + "\n")
    
    # Test cfpp_type with its exact description
    print("🏭 Testing cfpp_type field:")
    field_description = "classification of the plant whether it's private, public, or other types of ownership"
    print(f"📝 Description: {field_description}")
    
    content, sources = smart_field_search(plant_name, "cfpp_type", field_description)
    
    if content:
        print(f"✅ Ownership search found {len(content)} chars of content")
        print(f"🔗 Sources: {sources}")
        
        # Look for ownership-related terms
        ownership_terms = ["private", "public", "ownership", "company", "limited", "corporation"]
        found_terms = [term for term in ownership_terms if term.lower() in content.lower()]
        print(f"🏢 Ownership terms found: {found_terms}")
    else:
        print("❌ No ownership content found")

def test_query_formation_accuracy():
    """Verify that queries are formed exactly as expected."""
    print("\n🧪 Testing Query Formation Accuracy\n")
    
    plant_name = "Jhajjar Power Plant"
    
    # Test cases with expected queries
    test_cases = [
        {
            "field": "currency_in",
            "description": "ISO 4217 currency code of that power plant country",
            "expected_level1": '"Jhajjar Power Plant" "ISO 4217 currency code of that power plant country"'
        },
        {
            "field": "financial_year", 
            "description": "Fiscal year period in MM-MM format",
            "expected_level1": '"Jhajjar Power Plant" "Fiscal year period in MM-MM format"'
        }
    ]
    
    for test_case in test_cases:
        print(f"🎯 Field: {test_case['field']}")
        print(f"📝 Description: {test_case['description']}")
        print(f"🎯 Expected Query: {test_case['expected_level1']}")
        
        # This will print the actual query formed
        content, sources = smart_field_search(plant_name, test_case['field'], test_case['description'])
        
        print(f"✅ Query formation test completed")
        print("-" * 60)

if __name__ == "__main__":
    print("🚀 Testing Field Description-Based Query Formation\n")
    
    test_description_based_queries()
    test_specific_field_searches()
    test_query_formation_accuracy()
    
    print("\n✅ All description-based query tests completed!")
    print("\n🔧 Now using EXACT field descriptions for search queries:")
    print("   Level 1: Plant Name + Full Field Description")
    print("   Level 2: Plant Name + Key Terms from Description") 
    print("   Level 3: Field Description + India Context")
