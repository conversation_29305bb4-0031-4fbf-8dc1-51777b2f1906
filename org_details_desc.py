ORG_SCHEMA_DESC = {
    "cfpp_type": "classification of the plant whether it's private, public, or other types of ownership",
    "country_name": "full name of the country where the organization or plant is located",
    "currency_in": "ISO 4217 currency code of that power plant country",
    "financial_year": "Fiscal year period in MM-MM format",
    "organization_name": "Official name of the company/entity that owns the plant. It is not the same as any subsidary or share holder",
    "plants_count": "Number of power plant sites (plants) owned by the power plant. Counts distinct plant sites, not operational units",
    "plant_types": "Generation types, array of power plant technologies the organization operates as per the plants count",
    "ppa_flag": "Level at which the Power Purchase Agreement applies: 'Plant' (site-wide) or 'Unit' (individual generating unit)",
    "province": "Province or state sub-national region where the plant is based"
  }
