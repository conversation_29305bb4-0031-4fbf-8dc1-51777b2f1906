#!/usr/bin/env python3
"""
Test script to verify exact schema compliance with source tracking.
"""

import json
from unit_details_desc import UNIT_SCHEMA_DESC
from utils import search_google, scrape_text_from_links, build_prompt, call_llm

def test_unit_field_extraction():
    """Test extraction of a specific unit field with exact schema compliance."""
    
    # Test auxiliary_power_consumed field specifically
    field_schema = {
        "auxiliary_power_consumed": UNIT_SCHEMA_DESC["auxiliary_power_consumed"]
    }
    
    print("🧪 Testing auxiliary_power_consumed field extraction...")
    print(f"Expected schema: {json.dumps(field_schema, indent=2)}")
    
    # Search for specific data
    query = '"Jhajjar Power Plant" "auxiliary power consumed" percentage'
    links = search_google(query, num_results=3)
    
    if not links:
        print("❌ No search results found")
        return
    
    # Get content and sources
    content, sources = scrape_text_from_links(links, max_content_length=8000)
    
    if not content:
        print("❌ No content extracted")
        return
    
    print(f"📄 Content length: {len(content)} chars")
    print(f"🔗 Sources: {sources}")
    
    # Build prompt
    prompt = build_prompt("unit_details", field_schema, content)
    
    # Call LLM
    try:
        result = call_llm(prompt, max_tokens=1000, schema=field_schema, sources=sources)
        
        print("\n✅ EXTRACTED RESULT:")
        print(json.dumps(result, indent=2))
        
        # Validate structure
        if "auxiliary_power_consumed" in result:
            aux_data = result["auxiliary_power_consumed"]
            if isinstance(aux_data, list):
                print(f"✅ Correct array structure with {len(aux_data)} items")
                for item in aux_data:
                    if isinstance(item, dict) and "value" in item and "year" in item:
                        print(f"✅ Correct item structure: value={item['value']}, year={item['year']}")
                    else:
                        print(f"❌ Incorrect item structure: {item}")
            else:
                print(f"❌ Expected array, got: {type(aux_data)}")
        
        if "_sources" in result:
            print(f"✅ Sources included: {len(result['_sources'])} links")
        else:
            print("❌ No sources included")
            
    except Exception as e:
        print(f"❌ Extraction failed: {e}")

def test_fuel_type_extraction():
    """Test extraction of fuel_type field with complex nested structure."""
    
    field_schema = {
        "fuel_type": UNIT_SCHEMA_DESC["fuel_type"]
    }
    
    print("\n🧪 Testing fuel_type field extraction...")
    print(f"Expected schema: {json.dumps(field_schema, indent=2)}")
    
    # Search for specific data
    query = '"Jhajjar Power Plant" "fuel type" coal bituminous'
    links = search_google(query, num_results=3)
    
    if not links:
        print("❌ No search results found")
        return
    
    # Get content and sources
    content, sources = scrape_text_from_links(links, max_content_length=8000)
    
    if not content:
        print("❌ No content extracted")
        return
    
    # Build prompt
    prompt = build_prompt("unit_details", field_schema, content)
    
    # Call LLM
    try:
        result = call_llm(prompt, max_tokens=1000, schema=field_schema, sources=sources)
        
        print("\n✅ EXTRACTED RESULT:")
        print(json.dumps(result, indent=2))
        
        # Validate structure
        if "fuel_type" in result:
            fuel_data = result["fuel_type"]
            if isinstance(fuel_data, list):
                print(f"✅ Correct array structure with {len(fuel_data)} items")
                for item in fuel_data:
                    if isinstance(item, dict):
                        required_fields = ["fuel", "type", "years_percentage"]
                        missing = [f for f in required_fields if f not in item]
                        if not missing:
                            print(f"✅ Correct item structure: {item}")
                        else:
                            print(f"❌ Missing fields {missing} in: {item}")
                    else:
                        print(f"❌ Expected dict, got: {type(item)}")
            else:
                print(f"❌ Expected array, got: {type(fuel_data)}")
            
    except Exception as e:
        print(f"❌ Extraction failed: {e}")

if __name__ == "__main__":
    test_unit_field_extraction()
    test_fuel_type_extraction()
