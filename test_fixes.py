#!/usr/bin/env python3
"""
Test script to verify the fixes work properly.
"""

import sys
import os
from utils import search_google, scrape_text_from_links, call_llm

def test_search():
    """Test the improved search function."""
    print("🧪 Testing search functionality...")
    
    query = "Jhajjar Power Plant basic information"
    try:
        links = search_google(query, num_results=3)
        print(f"✅ Search returned {len(links)} links")
        return links
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        return []

def test_scraping(links):
    """Test the improved scraping function."""
    print("\n🧪 Testing scraping functionality...")
    
    if not links:
        print("⚠️ No links to test scraping")
        return ""
    
    try:
        content = scrape_text_from_links(links[:2], max_content_length=5000)
        print(f"✅ Scraping returned {len(content)} characters")
        return content
    except Exception as e:
        print(f"❌ Scraping test failed: {e}")
        return ""

def test_llm(content):
    """Test the improved LLM function."""
    print("\n🧪 Testing LLM functionality...")
    
    if not content:
        print("⚠️ No content to test LLM")
        return
    
    simple_schema = {
        "name": "string",
        "location": "string"
    }
    
    prompt = f"""Extract the following JSON structure:
{simple_schema}

Text:
{content[:2000]}
"""
    
    try:
        result = call_llm(prompt, max_tokens=1000)
        print(f"✅ LLM returned: {result}")
    except Exception as e:
        print(f"❌ LLM test failed: {e}")

def main():
    """Run all tests."""
    print("🚀 Starting fix verification tests...\n")
    
    # Test search
    links = test_search()
    
    # Test scraping
    content = test_scraping(links)
    
    # Test LLM
    test_llm(content)
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main()
