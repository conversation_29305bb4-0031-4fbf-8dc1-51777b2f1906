from org_processor import process_org_details
from plant_processor import process_plant_details
from unit_processor import process_unit_details

def run_pipeline(plant_name: str):
    org = process_org_details(plant_name)
    plant_results = process_plant_details(plant_name, int(org['plants_count']))
    all_unit_ids = []
    for plant in plant_results:
        all_unit_ids.extend(plant['units_id'])
    process_unit_details(plant_name, all_unit_ids)

if __name__ == "__main__":
    run_pipeline("Jhajjar Power Plant")
