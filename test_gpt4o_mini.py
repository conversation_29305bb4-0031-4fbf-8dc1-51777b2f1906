#!/usr/bin/env python3
"""
Test script to verify GPT-4o mini is working correctly.
"""

import json
from org_details_desc import ORG_SCHEMA_DESC
from utils import search_google, scrape_text_from_links, build_prompt, call_llm

def test_gpt4o_mini():
    """Test GPT-4o mini with a simple extraction task."""
    print("🧪 Testing GPT-4o mini for data extraction...")
    
    # Simple test schema
    test_schema = {
        "organization_name": "string",
        "country_name": "string", 
        "plant_types": ["array of strings"]
    }
    
    # Get some content about Jhajjar Power Plant
    query = '"Jhajjar Power Plant" organization details'
    links = search_google(query, num_results=2)
    
    if not links:
        print("❌ No search results found")
        return
    
    content, sources = scrape_text_from_links(links, max_content_length=5000)
    
    if not content:
        print("❌ No content extracted")
        return
    
    print(f"📄 Content length: {len(content)} chars")
    print(f"🔗 Sources: {len(sources)} URLs")
    
    # Build prompt
    prompt = build_prompt("test_extraction", test_schema, content)
    
    # Call LLM with GPT-4o mini
    try:
        result = call_llm(prompt, max_tokens=1000, schema=test_schema, sources=sources)
        
        print("\n✅ GPT-4o mini RESULT:")
        print(json.dumps(result, indent=2))
        
        # Validate the result
        if "organization_name" in result and result["organization_name"]:
            print("✅ Organization name extracted successfully")
        
        if "country_name" in result and result["country_name"]:
            print("✅ Country name extracted successfully")
            
        if "plant_types" in result and isinstance(result["plant_types"], list):
            print(f"✅ Plant types extracted: {result['plant_types']}")
            
        if "_sources" in result and len(result["_sources"]) > 0:
            print(f"✅ Sources tracked: {len(result['_sources'])} URLs")
        
        return True
        
    except Exception as e:
        print(f"❌ GPT-4o mini test failed: {e}")
        return False

def test_missing_field_with_gpt4o_mini():
    """Test missing field processing with GPT-4o mini."""
    print("\n🧪 Testing missing field processing with GPT-4o mini...")
    
    # Simulate data with missing currency_in field
    incomplete_data = {
        "country_name": "India",
        "organization_name": "CLP India Private Limited",
        "plants_count": 1,
        "plant_types": ["Coal"],
        "province": "Haryana"
        # Missing: currency_in
    }
    
    # Test extracting currency_in specifically
    field_schema = {"currency_in": ORG_SCHEMA_DESC["currency_in"]}
    
    # Search for currency information
    query = '"India" "currency" "INR" "ISO 4217"'
    links = search_google(query, num_results=2)
    
    if links:
        content, sources = scrape_text_from_links(links, max_content_length=3000)
        
        if content:
            prompt = build_prompt("currency_extraction", field_schema, content)
            
            try:
                result = call_llm(prompt, max_tokens=500, schema=field_schema, sources=sources)
                
                print("✅ Currency extraction result:")
                print(json.dumps(result, indent=2))
                
                if "currency_in" in result and result["currency_in"]:
                    print(f"✅ Successfully extracted currency: {result['currency_in']}")
                    return True
                else:
                    print("⚠️ Currency not found in result")
                    
            except Exception as e:
                print(f"❌ Currency extraction failed: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 Testing GPT-4o mini Integration\n")
    
    success1 = test_gpt4o_mini()
    success2 = test_missing_field_with_gpt4o_mini()
    
    if success1 and success2:
        print("\n✅ All GPT-4o mini tests passed!")
    else:
        print("\n⚠️ Some tests failed, but GPT-4o mini is configured")
    
    print("\n🔧 GPT-4o mini is now the primary model for all extractions.")
