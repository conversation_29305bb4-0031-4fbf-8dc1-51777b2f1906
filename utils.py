import os
import json
import hashlib
import requests
from bs4 import BeautifulSoup
from openai import OpenAI
import tempfile
from PyPDF2 import PdfReader
import urllib3
import time
import random
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


HEADERS = {"User-Agent": "Mozilla/5.0"}
SCRAPER_API_KEY = "b4aedbdc7dc14b48930567434b5f3f2d"
LLM_API_KEY = "********************************************************************************************************************************************************************"

# Create a session with retry strategy
def create_session():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

def cache_path(name: str):
    hashed = hashlib.md5(name.encode()).hexdigest()
    return os.path.join("cache", f"{hashed}.json")

def save_json(data: dict, path: str):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, 'w') as f:
        json.dump(data, f, indent=2)

def load_json(path: str) -> dict:
    with open(path) as f:
        return json.load(f)

def search_google(query: str, page: int = 1, num_results: int = 5, max_retries: int = 3) -> list:
    """Structured ScraperAPI Google Search with retry logic and fallback."""
    payload = {
        'api_key': SCRAPER_API_KEY,
        'query': query,
        'page': page,
        'num': 10  # max per page
    }
    url = 'https://api.scraperapi.com/structured/google/search'

    session = create_session()

    for attempt in range(max_retries):
        try:
            response = session.get(url, params=payload, timeout=30)
            response.raise_for_status()

            results = response.json().get('organic_results', [])
            print(f"\n🔗 Top {num_results} Search Results for: \"{query}\" (Page {page})")

            links = []
            for i, result in enumerate(results[:num_results]):
                link = result.get('link')
                title = result.get('title')
                if link:
                    print(f"{i+1}. {title} → {link}")
                    links.append(link)

            return links

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 500:
                print(f"⚠️ ScraperAPI server error (attempt {attempt + 1}/{max_retries}). Retrying...")
                time.sleep(2 ** attempt + random.uniform(0, 1))  # Exponential backoff
                continue
            else:
                raise e
        except Exception as e:
            print(f"⚠️ Search failed (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt + random.uniform(0, 1))
                continue
            else:
                print(f"❌ All ScraperAPI attempts failed for query: {query}")
                print("🔄 Trying fallback search method...")
                return fallback_search(query, num_results)

    print("🔄 Trying fallback search method...")
    return fallback_search(query, num_results)


def fallback_search(query: str, num_results: int = 5) -> list:
    """Fallback search using direct Google search when ScraperAPI fails."""
    try:
        import urllib.parse
        encoded_query = urllib.parse.quote_plus(query)
        search_url = f"https://www.google.com/search?q={encoded_query}&num={num_results}"

        session = create_session()
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = session.get(search_url, headers=headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')
        links = []

        # Extract links from search results
        for result in soup.find_all('div', class_='g'):
            link_elem = result.find('a')
            if link_elem and link_elem.get('href'):
                href = link_elem.get('href')
                if href.startswith('http'):
                    links.append(href)
                    if len(links) >= num_results:
                        break

        print(f"\n🔗 Fallback search found {len(links)} results for: \"{query}\"")
        for i, link in enumerate(links):
            print(f"{i+1}. {link}")

        return links

    except Exception as e:
        print(f"⚠️ Fallback search also failed: {e}")
        return []


def scrape_text_from_links(links: list, max_content_length: int = 15000) -> tuple[str, list]:
    """Scrape text from links with improved error handling and content management."""
    content = ""
    successful_sources = []
    session = create_session()

    for link in links:
        try:
            # Try ScraperAPI first
            final_url = f"http://api.scraperapi.com/?api_key={SCRAPER_API_KEY}&url={link}"
            res = session.get(final_url, headers=HEADERS, timeout=15)
            res.raise_for_status()

            soup = BeautifulSoup(res.text, "html.parser")

        except Exception as scraper_error:
            print(f"⚠️ ScraperAPI failed for {link}: {scraper_error}")
            try:
                # Fallback to direct scraping
                print(f"🔄 Trying direct scraping for {link}")
                res = session.get(link, headers=HEADERS, timeout=10, verify=False)
                res.raise_for_status()
                soup = BeautifulSoup(res.text, "html.parser")

            except Exception as direct_error:
                print(f"⚠️ Direct scraping also failed for {link}: {direct_error}")
                continue

        try:
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            text = soup.get_text(separator="\n")

            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            text = '\n'.join(line for line in lines if line)

            content += text + "\n"
            successful_sources.append(link)

            # Stop if we have enough content
            if len(content) > max_content_length:
                break

        except Exception as e:
            print(f"⚠️ Failed to process content from {link}: {e}")
            continue

    return content[:max_content_length], successful_sources


def get_search_cache_path():
    """Get the path for search cache directory."""
    cache_dir = "/Users/<USER>/Downloads/power_plant_project/cache/search_cache"
    os.makedirs(cache_dir, exist_ok=True)
    return cache_dir


def get_url_cache_key(url: str) -> str:
    """Generate a cache key for a URL."""
    import hashlib
    return hashlib.md5(url.encode()).hexdigest()


def save_url_content_to_cache(url: str, content: str):
    """Save scraped content to cache."""
    cache_dir = get_search_cache_path()
    cache_key = get_url_cache_key(url)
    cache_file = os.path.join(cache_dir, f"{cache_key}.json")

    cache_data = {
        "url": url,
        "content": content,
        "timestamp": time.time(),
        "content_length": len(content)
    }

    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(cache_data, f, ensure_ascii=False, indent=2)

    print(f"💾 Cached content for {url} ({len(content)} chars)")


def load_url_content_from_cache(url: str) -> str:
    """Load content from cache if available."""
    cache_dir = get_search_cache_path()
    cache_key = get_url_cache_key(url)
    cache_file = os.path.join(cache_dir, f"{cache_key}.json")

    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # Check if cache is not too old (24 hours)
            if time.time() - cache_data.get("timestamp", 0) < 86400:
                print(f"📁 Using cached content for {url} ({cache_data.get('content_length', 0)} chars)")
                return cache_data.get("content", "")
            else:
                print(f"⏰ Cache expired for {url}, will re-scrape")
                os.remove(cache_file)  # Remove expired cache
        except Exception as e:
            print(f"⚠️ Error loading cache for {url}: {e}")

    return None


def scrape_text_from_links_with_cache(links: list, max_content_length: int = 15000) -> tuple[str, list]:
    """Scrape text from links with caching to avoid re-scraping same URLs."""
    content = ""
    successful_sources = []
    session = create_session()

    for link in links:
        # Check cache first
        cached_content = load_url_content_from_cache(link)

        if cached_content is not None:
            # Use cached content
            content += cached_content + "\n"
            successful_sources.append(link)

            # Stop if we have enough content
            if len(content) > max_content_length:
                break
            continue

        # Not in cache, scrape it
        try:
            # Use ScraperAPI first
            final_url = f"http://api.scraperapi.com/?api_key={SCRAPER_API_KEY}&url={link}"
            res = session.get(final_url, headers=HEADERS, timeout=15)
            res.raise_for_status()

            soup = BeautifulSoup(res.text, "html.parser")

        except Exception as scraper_error:
            print(f"⚠️ ScraperAPI failed for {link}: {scraper_error}")
            try:
                # Fallback to direct scraping
                print(f"🔄 Trying direct scraping for {link}")
                res = session.get(link, headers=HEADERS, timeout=10, verify=False)
                res.raise_for_status()
                soup = BeautifulSoup(res.text, "html.parser")

            except Exception as direct_error:
                print(f"⚠️ Direct scraping also failed for {link}: {direct_error}")
                continue

        try:
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            text = soup.get_text(separator="\n")

            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            text = '\n'.join(line for line in lines if line)

            # Save to cache
            save_url_content_to_cache(link, text)

            content += text + "\n"
            successful_sources.append(link)

            # Stop if we have enough content
            if len(content) > max_content_length:
                break

        except Exception as e:
            print(f"⚠️ Failed to process content from {link}: {e}")
            continue

    return content[:max_content_length], successful_sources


def smart_field_search(plant_name: str, field_name: str, field_description: str, max_retries: int = 3) -> tuple[str, list]:
    """
    Multi-level search strategy using FIELD DESCRIPTIONS for query formation:
    Level 1: Plant name + field description (MAIN APPROACH)
    Level 2: Plant name + key terms from description
    Level 3: Field description + India context
    """

    # Level 1: Plant name + FULL field description (PRIMARY SEARCH)
    level1_query = f'"{plant_name}" "{field_description}"'
    print(f"🔍 Level 1 Search (Plant + Description): {level1_query}")

    links = search_google(level1_query, num_results=5)
    if links:
        content, sources = scrape_text_from_links_with_cache(links, max_content_length=12000)
        if content.strip():
            print(f"✅ Level 1 found content ({len(content)} chars)")
            return content, sources

    # Level 2: Plant name + key terms from field description
    description_keywords = extract_keywords_from_description(field_description)
    level2_query = f'"{plant_name}" {" ".join(description_keywords[:4])}'
    print(f"🔍 Level 2 Search (Plant + Keywords): {level2_query}")

    links = search_google(level2_query, num_results=5)
    if links:
        content, sources = scrape_text_from_links_with_cache(links, max_content_length=12000)
        if content.strip():
            print(f"✅ Level 2 found content ({len(content)} chars)")
            return content, sources

    # Level 3: Field description + India context (NO plant name)
    level3_query = f'"{field_description}" India power plant'
    print(f"🔍 Level 3 Search (Description + Context): {level3_query}")

    links = search_google(level3_query, num_results=5)
    if links:
        content, sources = scrape_text_from_links_with_cache(links, max_content_length=12000)
        if content.strip():
            print(f"✅ Level 3 found content ({len(content)} chars)")
            return content, sources

    print(f"❌ All search levels failed for {field_name}")
    return "", []


def extract_keywords_from_description(description: str) -> list:
    """Extract meaningful keywords from field description."""
    import re

    # Remove common words and extract meaningful terms
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'}

    # Clean and split description
    words = re.findall(r'\b[a-zA-Z]+\b', description.lower())
    keywords = [word for word in words if len(word) > 3 and word not in stop_words]

    # Prioritize technical terms
    priority_terms = ['capacity', 'efficiency', 'emission', 'fuel', 'power', 'energy', 'plant', 'unit', 'generation', 'auxiliary', 'heat', 'rate', 'factor', 'biomass', 'coal', 'natural', 'gas']

    prioritized = [k for k in keywords if k in priority_terms]
    others = [k for k in keywords if k not in priority_terms]

    return prioritized + others[:5]  # Return top technical terms + other relevant terms


def find_missing_fields(current_data: dict, schema: dict) -> list:
    """Find fields that are missing or null in current data."""
    missing_fields = []

    def check_missing(data, schema_part, prefix=""):
        for key, schema_value in schema_part.items():
            full_key = f"{prefix}.{key}" if prefix else key

            if key not in data or data[key] is None:
                missing_fields.append((full_key, key, schema_value))
            elif isinstance(schema_value, dict) and isinstance(data.get(key), dict):
                check_missing(data[key], schema_value, full_key)
            elif isinstance(schema_value, list) and schema_value and isinstance(schema_value[0], dict):
                if not data.get(key) or (isinstance(data[key], list) and len(data[key]) == 0):
                    missing_fields.append((full_key, key, schema_value))

    check_missing(current_data, schema)
    return missing_fields


def process_missing_fields(plant_name: str, current_data: dict, schema: dict, cache_path: str) -> dict:
    """Process missing fields using multi-level search strategy."""
    missing_fields = find_missing_fields(current_data, schema)

    if not missing_fields:
        print("✅ No missing fields found")
        return current_data

    print(f"🔍 Found {len(missing_fields)} missing fields: {[f[1] for f in missing_fields]}")

    updated_data = current_data.copy()

    for full_key, field_name, field_schema in missing_fields:
        print(f"\n🎯 Processing missing field: {field_name}")

        # Get field description
        if isinstance(field_schema, str):
            field_description = field_schema
        elif isinstance(field_schema, list) and field_schema:
            field_description = str(field_schema[0])
        else:
            field_description = f"Information about {field_name.replace('_', ' ')}"

        # Use smart search
        content, sources = smart_field_search(plant_name, field_name, field_description)

        if content:
            # Create subschema for this field
            subschema = {field_name: field_schema}

            # Build prompt and extract data
            prompt = build_prompt("missing_field_extraction", subschema, content)

            try:
                # Determine workspace type from cache path
                workspace_type = "org"
                if "plant_cache" in cache_path:
                    workspace_type = "plant"
                elif "unit_cache" in cache_path:
                    workspace_type = "unit"

                result = call_llm(prompt, max_tokens=1500, schema=subschema, sources=sources,
                                plant_name=plant_name, field_name=field_name, workspace_type=workspace_type)

                if result and field_name in result and result[field_name] is not None:
                    updated_data[field_name] = result[field_name]
                    print(f"✅ Successfully extracted {field_name}: {result[field_name]}")
                else:
                    print(f"⚠️ No valid data found for {field_name}")

            except Exception as e:
                print(f"❌ Failed to extract {field_name}: {e}")
        else:
            print(f"❌ No content found for {field_name}")

    # Remove any _sources from the main data (they're saved separately now)
    if "_sources" in updated_data:
        del updated_data["_sources"]

    # Save updated data to cache
    save_json(updated_data, cache_path)
    print(f"💾 Updated cache with {len([f for f in missing_fields if f[1] in updated_data])} new fields")

    return updated_data


def validate_and_clean_json(result: dict, schema: dict) -> dict:
    """Validate and clean the extracted JSON to match the schema exactly."""
    if not isinstance(result, dict):
        return {}

    cleaned = {}

    def clean_value(value, schema_value):
        """Clean a value according to its schema definition."""
        if isinstance(schema_value, dict):
            if isinstance(value, dict):
                return validate_and_clean_json(value, schema_value)
            else:
                return {}
        elif isinstance(schema_value, list) and schema_value:
            # Handle array fields properly
            if isinstance(value, list):
                if isinstance(schema_value[0], dict):
                    # Validate each item in the array against the schema
                    cleaned_items = []
                    for item in value:
                        if isinstance(item, dict):
                            cleaned_item = validate_and_clean_json(item, schema_value[0])
                            if cleaned_item:  # Only add non-empty items
                                cleaned_items.append(cleaned_item)
                    return cleaned_items
                else:
                    return value
            elif value is None:
                return []
            else:
                # Try to convert single values to array format if needed
                if isinstance(schema_value[0], dict) and isinstance(value, dict):
                    cleaned_item = validate_and_clean_json(value, schema_value[0])
                    return [cleaned_item] if cleaned_item else []
                return []
        else:
            return value

    # Only include fields that exist in the schema
    for key, schema_value in schema.items():
        if key in result:
            cleaned[key] = clean_value(result[key], schema_value)

    return cleaned

def build_prompt(schema_name: str, schema_description: dict, text: str) -> str:
    examples = '''IMPORTANT ARRAY STRUCTURE EXAMPLES:
- "auxiliary_power_consumed": [{"value": 6.64, "year": "2023"}]
- "emission_factor": [{"value": 0.82, "year": "2022"}]
- "fuel_type": [{"fuel": "Coal", "type": "bituminous", "years_percentage": {"2023": "100"}}]'''

    return f"""You are a data extraction expert. Extract ONLY the fields specified in the schema below from the provided text.

CRITICAL REQUIREMENTS:
1. Return ONLY the exact fields defined in the schema - NO additional fields
2. Use the exact field names as specified in the schema
3. Follow the exact data types and structure shown in the schema
4. For array fields like "auxiliary_power_consumed", "emission_factor", "fuel_type", "plf", etc., return arrays of objects with "value" and "year" fields
5. For simple fields like "capacity", "technology", return the actual values
6. If a field cannot be found in the text, either omit it or use null
7. Do not add any explanatory text or extra information
8. Return only valid JSON that matches the schema structure exactly
9. Extract real data from the text, not placeholder descriptions

{examples}

Schema for '{schema_name}':
{json.dumps(schema_description, indent=2)}

Text to extract from:
{text}

Return only the JSON object that matches the schema exactly:"""

def save_sources_separately(plant_name: str, field_name: str, sources: list, workspace_type: str = "org"):
    """Save source links in a separate JSON file."""
    sources_dir = f"/Users/<USER>/Downloads/power_plant_project/workspace/{workspace_type}_sources"
    os.makedirs(sources_dir, exist_ok=True)

    sources_file = os.path.join(sources_dir, f"{workspace_type}_sources_{plant_name}.json")

    # Load existing sources or create new
    if os.path.exists(sources_file):
        with open(sources_file, 'r') as f:
            all_sources = json.load(f)
    else:
        all_sources = {}

    # Add sources for this field
    if field_name not in all_sources:
        all_sources[field_name] = []

    # Add new sources (avoid duplicates)
    for source in sources:
        if source not in all_sources[field_name]:
            all_sources[field_name].append(source)

    # Save updated sources
    with open(sources_file, 'w') as f:
        json.dump(all_sources, f, indent=2)

    print(f"💾 Saved {len(sources)} sources for {field_name} to {sources_file}")


def chunk_content_for_rag(content: str, chunk_size: int = 4000) -> list:
    """Split content into chunks for RAG processing."""
    words = content.split()
    chunks = []
    current_chunk = []
    current_length = 0

    for word in words:
        word_length = len(word) + 1  # +1 for space
        if current_length + word_length > chunk_size and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = [word]
            current_length = word_length
        else:
            current_chunk.append(word)
            current_length += word_length

    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks


def extract_with_rag(content: str, schema: dict, schema_name: str, max_chunk_size: int = 4000) -> dict:
    """Use RAG method to extract data from large content without truncation."""
    if len(content) <= max_chunk_size:
        # Content is small enough, process normally
        prompt = build_prompt(schema_name, schema, content)
        return call_llm_basic(prompt, schema=schema)

    print(f"📚 Content too large ({len(content)} chars), using RAG method...")

    # Split content into chunks
    chunks = chunk_content_for_rag(content, max_chunk_size)
    print(f"📄 Split into {len(chunks)} chunks")

    # Extract from each chunk
    all_results = []
    for i, chunk in enumerate(chunks):
        print(f"🔍 Processing chunk {i+1}/{len(chunks)}...")

        prompt = build_prompt(f"{schema_name}_chunk_{i+1}", schema, chunk)
        try:
            chunk_result = call_llm_basic(prompt, schema=schema, max_tokens=2000)
            if chunk_result:
                all_results.append(chunk_result)
        except Exception as e:
            print(f"⚠️ Failed to process chunk {i+1}: {e}")
            continue

    # Merge results from all chunks
    if not all_results:
        return {}

    # Combine results intelligently
    merged_result = merge_rag_results(all_results, schema)
    print(f"✅ RAG processing complete: merged {len(all_results)} chunk results")

    return merged_result


def merge_rag_results(results: list, schema: dict) -> dict:
    """Merge results from multiple RAG chunks intelligently."""
    merged = {}

    for key, schema_value in schema.items():
        values_found = []

        # Collect all non-null values for this field
        for result in results:
            if key in result and result[key] is not None:
                values_found.append(result[key])

        if not values_found:
            merged[key] = None
            continue

        # Handle different data types
        if isinstance(schema_value, list):
            # For arrays, combine all unique items
            combined_array = []
            for value in values_found:
                if isinstance(value, list):
                    combined_array.extend(value)
                else:
                    combined_array.append(value)

            # Remove duplicates while preserving order
            seen = set()
            unique_array = []
            for item in combined_array:
                item_str = str(item)
                if item_str not in seen:
                    seen.add(item_str)
                    unique_array.append(item)

            merged[key] = unique_array

        elif isinstance(schema_value, dict):
            # For objects, merge recursively
            merged[key] = merge_rag_results(values_found, schema_value)

        else:
            # For simple values, take the first non-null value
            merged[key] = values_found[0]

    return merged


def call_llm_basic(prompt: str, max_tokens: int = 4000, schema: dict = None) -> dict:
    """Basic LLM call without source tracking (for RAG chunks)."""
    client = OpenAI(api_key=LLM_API_KEY)

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=max_tokens,
            temperature=0.1
        )
        raw = response.choices[0].message.content.strip()

        # Extract JSON
        import re
        json_text = ""
        code_block_match = re.search(r"```(?:json)?\s*({.*?})\s*```", raw, re.DOTALL)
        if code_block_match:
            json_text = code_block_match.group(1).strip()
        else:
            json_match = re.search(r"({.*})", raw, re.DOTALL)
            if json_match:
                json_text = json_match.group(1).strip()

        if json_text:
            result = json.loads(json_text)
            if schema:
                result = validate_and_clean_json(result, schema)
            return result

    except Exception as e:
        print(f"⚠️ LLM call failed: {e}")

    return {}


def call_llm(prompt: str, max_tokens: int = 4000, schema: dict = None, sources: list = None, plant_name: str = None, field_name: str = None, workspace_type: str = "org") -> dict:
    """Call LLM with RAG support and separate source tracking."""
    import re

    # Save sources separately if provided
    if sources and plant_name and field_name:
        save_sources_separately(plant_name, field_name, sources, workspace_type)

    # Check if we need to use RAG for long content
    if len(prompt) > max_tokens * 3:  # Rough estimate: 1 token ≈ 3 chars
        print(f"📚 Prompt too long ({len(prompt)} chars), using RAG method...")

        # Extract content from prompt for RAG processing
        content_start = prompt.find("Text to extract from:")
        if content_start != -1:
            content = prompt[content_start + len("Text to extract from:"):].strip()
            schema_part = prompt[:content_start]

            # Use RAG to process the content
            if schema:
                schema_name = "data_extraction"
                return extract_with_rag(content, schema, schema_name, max_chunk_size=3000)

    # Normal processing for shorter content
    client = OpenAI(api_key=LLM_API_KEY)

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=min(max_tokens, 4000),
            temperature=0.1
        )
        raw = response.choices[0].message.content.strip()
        print(f"\n🔎 LLM RAW OUTPUT (using gpt-4o-mini):\n", raw)

        json_text = ""

        # Attempt to extract JSON from code blocks
        code_block_match = re.search(r"```(?:json)?\s*({.*?})\s*```", raw, re.DOTALL)
        if code_block_match:
            json_text = code_block_match.group(1).strip()
        else:
            # Attempt to extract first JSON object from any part of the text
            json_match = re.search(r"({.*})", raw, re.DOTALL)
            if json_match:
                json_text = json_match.group(1).strip()

        if not json_text:
            print("❌ No JSON content could be extracted from the response.")
            raise ValueError("LLM response did not contain JSON.")

        print("\n✅ Extracted JSON content:\n", json_text)

        try:
            result = json.loads(json_text)

            # Validate and clean the result if schema is provided
            if schema:
                result = validate_and_clean_json(result, schema)
                print(f"✅ Result validated and cleaned according to schema")

            # Don't add sources to the main result anymore - they're saved separately
            return result

        except json.JSONDecodeError as e:
            print(f"❌ JSON decode failed.")
            raise e

    except Exception as e:
        print(f"⚠️ Error with LLM: {e}")
        raise e


def scrape_text_from_pdfs(links: list) -> str:
    """Download and extract text from PDF links, with SSL handling."""
    text = ""
    for url in links:
        if url.lower().endswith(".pdf"):
            try:
                response = requests.get(url, verify=False, timeout=10)  # Ignore cert errors
                response.raise_for_status()
                with tempfile.NamedTemporaryFile(delete=True, suffix=".pdf") as tmp:
                    tmp.write(response.content)
                    tmp.flush()
                    reader = PdfReader(tmp.name)
                    for page in reader.pages:
                        text += page.extract_text() or ""
            except Exception as e:
                print(f"⚠️ Failed to process PDF: {url}\nReason: {e}")
    return text

