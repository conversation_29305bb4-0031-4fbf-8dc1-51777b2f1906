import os
import json
from unit_details_desc import UNIT_SCHEMA_DESC
from utils import cache_path, save_json, load_json, search_google, scrape_text_from_links, scrape_text_from_pdfs, build_prompt, call_llm, process_missing_fields
from collections import defaultdict

CACHE_DIR = "/Users/<USER>/Downloads/power_plant_project/cache/unit_cache"
WORKSPACE_DIR = "/Users/<USER>/Downloads/power_plant_project/workspace/unit_details"

FIELD_KEYWORDS_UNIT = {
    "auxiliary_power_consumed": ["auxiliary", "internal use", "AUX"],
    "boiler_type": ["boiler", "type of boiler"],
    "capacity": ["installed capacity", "MW", "unit capacity"],
    "capex_required_renovation_closed_cycle": ["closed cycle", "CCGT", "renovation cost", "retrofit", "conversion"],
    "capex_required_renovation_open_cycle": ["open cycle", "OCGT", "retrofitting", "capex"],
    "capex_required_retrofit": ["retrofit", "cofire", "conversion", "biomass"],
    "closed_cylce_gas_turbine_efficency": ["ccgt efficiency", "closed cycle efficiency"],
    "combined_cycle_heat_rate": ["combined cycle", "heat rate", "efficiency"],
    "commencement_date": ["commissioned", "start date", "operation date"],
    "efficiency_loss_cofiring": ["efficiency loss", "cofiring impact", "performance drop"],
    "emission_factor": ["emission", "co2", "carbon", "per kwh", "kg"],
    "fuel_type": ["fuel", "biomass", "coal", "natural gas", "type"],
    "gcv_biomass": ["biomass GCV", "calorific value", "kcal/kg"],
    "gcv_coal": ["coal GCV", "kcal/kg", "energy content"],
    "gcv_natural_gas": ["natural gas GCV", "mj/m3", "kcal", "btu"],
    "gross_power_generation": ["generation", "total electricity", "gross output"],
    "heat_rate": ["station heat rate", "kcal/kwh", "efficiency"],
    "open_cycle_gas_turbine_efficency": ["ocgt efficiency", "open cycle performance"],
    "open_cycle_heat_rate": ["heat rate", "ocgt", "open cycle"],
    "PAF": ["availability", "PAF", "plant uptime"],
    "plf": ["load factor", "PLF", "capacity utilization"],
    "ppa_details": ["ppa", "contract", "tariff", "buyer", "duration"],
    "remaining_useful_life": ["lifetime", "end of life", "years remaining"],
    "selected_biomass_type": ["biomass", "fuel type"],
    "selected_coal_type": ["coal type", "bituminous", "sub-bituminous"],
    "technology": ["sub-critical", "supercritical", "cycle", "technology used"],
    "unit": ["efficiency", "unit %"],
    "unit_efficiency": ["conversion efficiency", "unit performance"],
    "unit_lifetime": ["years", "life span", "expected life"],
    "unit_number": ["unit id", "label", "number"]
}


def flatten_keys(d, prefix=''):
    keys = []
    if isinstance(d, dict):
        for k, v in d.items():
            full_key = f"{prefix}.{k}" if prefix else k
            keys.extend(flatten_keys(v, full_key))
    elif isinstance(d, list) and d and isinstance(d[0], dict):
        for k, v in d[0].items():
            full_key = f"{prefix}[].{k}" if prefix else k
            keys.extend(flatten_keys(v, full_key))
    else:
        keys.append(prefix)
    return keys

def group_fields_by_top_level(flat_keys):
    groups = defaultdict(list)
    for key in flat_keys:
        top = key.split(".")[0].split("[]")[0]
        groups[top].append(key)
    return groups

def extract_subschema(schema: dict, keys: list) -> dict:
    extracted = {}
    for k in keys:
        parts = k.replace("[]", "").split(".")
        d = extracted
        s = schema
        for i, p in enumerate(parts):
            if p not in s:
                break
            if i == len(parts) - 1:
                d[p] = s[p]
            else:
                d = d.setdefault(p, {})
                s = s[p]
    return extracted


def filter_relevant_text(raw_text: str, field_key: str, field_map: dict, max_length: int = 8000) -> str:
    """Filter text to only include relevant lines based on keywords."""
    keywords = field_map.get(field_key.lower(), [])
    if not keywords:
        return raw_text[:max_length]

    filtered_lines = []
    lines = raw_text.splitlines()

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        # Check if line contains any relevant keywords
        if any(kw.lower() in line.lower() for kw in keywords):
            # Add some context (previous and next lines)
            start_idx = max(0, i - 1)
            end_idx = min(len(lines), i + 2)
            context_lines = [lines[j].strip() for j in range(start_idx, end_idx) if lines[j].strip()]
            filtered_lines.extend(context_lines)

    # Remove duplicates while preserving order
    seen = set()
    unique_lines = []
    for line in filtered_lines:
        if line not in seen:
            seen.add(line)
            unique_lines.append(line)

    result = "\n".join(unique_lines)
    return result[:max_length]

def format_query(plant_name: str, field_key: str, uid: str | None = None) -> str:
    """
    Create a structured search query for Google based on the field key and optional unit ID.
    """
    # Clean nested path into human-readable keywords
    field_text = field_key.replace("[].", " ").replace(".", " ").replace("_", " ").strip()
    if uid:
        return f'"{plant_name}" "unit {uid} {field_text}"'
    else:
        return f'"{plant_name}" "{field_text}"'

def process_unit_details(plant_name: str, units_ids: list) -> list:
    results = []
    flattened_fields = flatten_keys(UNIT_SCHEMA_DESC)
    grouped_fields = group_fields_by_top_level(flattened_fields)

    for uid in units_ids:
        cache_key = f"unit_{plant_name}_{uid}"
        cached = cache_path(cache_key)

        if os.path.exists(cached):
            unit_data = load_json(cached)
            print(f"📁 Loading cached unit data for {plant_name} (unit {uid})")

            # Check for missing fields and process them
            print("🔍 Checking for missing fields in cached unit data...")
            unit_data = process_missing_fields(plant_name, unit_data, UNIT_SCHEMA_DESC, cached)
        else:
            unit_data = {}

            for top_field, field_keys in grouped_fields.items():
                print(f"\n🔍 Processing unit field group: {top_field}")

                subschema = extract_subschema(UNIT_SCHEMA_DESC, field_keys)
                query = format_query(plant_name, top_field)

                # Search with retry logic
                links = search_google(query, page=1, num_results=3)

                if not links:
                    print(f"⚠️ No search results for {top_field}, skipping...")
                    continue

                # Get content from links and PDFs
                combined_text, sources = scrape_text_from_links(links, max_content_length=10000)
                pdf_text = scrape_text_from_pdfs(links)
                combined_text += pdf_text

                if not combined_text.strip():
                    print(f"⚠️ No content extracted for {top_field}, skipping...")
                    continue

                # Filter and limit text to avoid token limits
                filtered_text = filter_relevant_text(combined_text, top_field, FIELD_KEYWORDS_UNIT, max_length=6000)

                if not filtered_text.strip():
                    print(f"⚠️ No relevant content found for {top_field}, skipping...")
                    continue

                prompt = build_prompt("unit_details", subschema, filtered_text)

                try:
                    partial_result = call_llm(prompt, max_tokens=2000, schema=subschema, sources=sources)

                    if isinstance(partial_result, dict):
                        unit_data.update(partial_result)
                        print(f"✅ Successfully extracted data for {top_field}")
                    else:
                        print(f"⚠️ Invalid result format for {top_field}")

                except Exception as e:
                    print(f"❌ Failed to process {top_field}: {e}")
                    continue

            # Save initial results and process missing fields
            save_json(unit_data, cached)

            print("🔍 Checking for missing fields in initial unit extraction...")
            unit_data = process_missing_fields(plant_name, unit_data, UNIT_SCHEMA_DESC, cached)

        results.append(unit_data)

    save_json(results, os.path.join(WORKSPACE_DIR, f"unit_details_{plant_name}.json"))
    return results
