PLANT_SCHEMA_DESC = {
    "grid_connectivity_maps": [
     {
      "details": [
       {
        "capacity": "The rated capacity of the connection at this substation (e.g., in MW)",
        "latitude": "The geographic latitude of the substation",
        "longitude": "The geographic longitude of the substation",
        "projects": [
         {
          "distance": "The distance (e.g., in km) from the substation to that project"
         }
        ],
        "substation_name": "The official name of the substation",
        "substation_type": "The classification and voltage level of the substation, including any regional or directional qualifier"
       }
      ]
     }
    ],
    "lat": "The plant's own latitude coordinate (decimal degrees)",
    "long": "The plant's own longitude coordinate (decimal degrees)",
    "name": "The official name of the power plant",
    "plant_address": "District or city, State, Country",
    "plant_id": "A unique identifier assigned to this plant in your system (integer)",
    "plant_type": "The technology or fuel type of the plant site",
    "ppa_details": [
     {
      "capacity": "The capacity covered by this PPA (typically in MW)",
      "capacity_unit": "The unit of that capacity (e.g., 'MW', 'kW')",
      "end_date": "The PPA's termination date (ISO format, YYYY-MM-DD). Typically 25 years from the start date.",
      "respondents": [
       {
        "capacity": "The capacity volume contracted by this respondent",
        "currency": "The currency in which the price is denominated (e.g., 'USD', 'INR')",
        "name": "The counterparty's name (utility, trader, corporate buyer, etc.)",
        "price": "The contracted price per unit of energy or capacity",
        "price_unit": "The basis for the price (e.g., '$/MWh', 'INR/kW-year')"
       }
      ],
      "start_date": "The PPA's commencement date (ISO format, YYYY-MM-DD)",
      "tenure": "The numeric duration of the PPA (e.g., 20)",
      "tenure_type": "The unit for the tenure (e.g., 'Years', 'Months')"
     }
    ],
    "units_id": [
     "List of units which are operational at this plant"
    ]
}