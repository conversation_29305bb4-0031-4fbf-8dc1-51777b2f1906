#!/usr/bin/env python3
"""
Test script to verify URL caching is working properly.
"""

import os
import time
from utils import search_google, scrape_text_from_links_with_cache, get_search_cache_path

def test_url_caching():
    """Test that URLs are cached and reused."""
    print("🧪 Testing URL Caching System\n")
    
    # Clear cache for clean test
    cache_dir = get_search_cache_path()
    print(f"📁 Cache directory: {cache_dir}")
    
    # Test search
    query = '"Jhajjar Power Plant" organization'
    print(f"🔍 Searching: {query}")
    
    links = search_google(query, num_results=3)
    
    if not links:
        print("❌ No search results found")
        return False
    
    print(f"🔗 Found {len(links)} links:")
    for i, link in enumerate(links):
        print(f"  {i+1}. {link}")
    
    # First scrape - should cache the content
    print("\n🚀 First scrape (should cache content):")
    start_time = time.time()
    content1, sources1 = scrape_text_from_links_with_cache(links, max_content_length=5000)
    first_duration = time.time() - start_time
    
    print(f"✅ First scrape completed in {first_duration:.2f} seconds")
    print(f"📄 Content length: {len(content1)} chars")
    print(f"🔗 Sources: {len(sources1)} URLs")
    
    # Check cache files were created
    cache_files = os.listdir(cache_dir)
    print(f"💾 Cache files created: {len(cache_files)}")
    
    # Second scrape - should use cached content
    print("\n⚡ Second scrape (should use cache):")
    start_time = time.time()
    content2, sources2 = scrape_text_from_links_with_cache(links, max_content_length=5000)
    second_duration = time.time() - start_time
    
    print(f"✅ Second scrape completed in {second_duration:.2f} seconds")
    print(f"📄 Content length: {len(content2)} chars")
    print(f"🔗 Sources: {len(sources2)} URLs")
    
    # Verify results
    print(f"\n📊 Performance Comparison:")
    print(f"  First scrape:  {first_duration:.2f} seconds")
    print(f"  Second scrape: {second_duration:.2f} seconds")
    print(f"  Speed improvement: {(first_duration / second_duration):.1f}x faster")
    
    # Verify content is the same
    if content1 == content2:
        print("✅ Content matches - caching working correctly")
        return True
    else:
        print("❌ Content doesn't match - caching issue")
        return False

def test_cache_expiry():
    """Test that cache expiry works (simulated)."""
    print("\n🧪 Testing Cache Expiry Logic\n")
    
    from utils import load_url_content_from_cache, save_url_content_to_cache
    
    test_url = "https://example.com/test"
    test_content = "This is test content for cache expiry"
    
    # Save content to cache
    save_url_content_to_cache(test_url, test_content)
    
    # Load from cache (should work)
    cached_content = load_url_content_from_cache(test_url)
    
    if cached_content == test_content:
        print("✅ Cache save and load working")
    else:
        print("❌ Cache save/load failed")
        return False
    
    # Simulate expired cache by modifying timestamp
    cache_dir = get_search_cache_path()
    from utils import get_url_cache_key
    cache_key = get_url_cache_key(test_url)
    cache_file = os.path.join(cache_dir, f"{cache_key}.json")
    
    if os.path.exists(cache_file):
        import json
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        # Set timestamp to 25 hours ago (expired)
        cache_data['timestamp'] = time.time() - (25 * 3600)
        
        with open(cache_file, 'w') as f:
            json.dump(cache_data, f)
        
        # Try to load expired cache
        expired_content = load_url_content_from_cache(test_url)
        
        if expired_content is None:
            print("✅ Cache expiry working - expired content not returned")
            return True
        else:
            print("❌ Cache expiry not working - expired content returned")
            return False
    
    return False

def test_multiple_field_caching():
    """Test caching across multiple field searches."""
    print("\n🧪 Testing Multiple Field Caching\n")
    
    from utils import smart_field_search
    from org_details_desc import ORG_SCHEMA_DESC
    
    plant_name = "Jhajjar Power Plant"
    
    # Test multiple fields that might hit same URLs
    fields_to_test = [
        ("cfpp_type", ORG_SCHEMA_DESC["cfpp_type"]),
        ("organization_name", ORG_SCHEMA_DESC["organization_name"]),
    ]
    
    total_time = 0
    
    for field_name, field_description in fields_to_test:
        print(f"🎯 Testing field: {field_name}")
        
        start_time = time.time()
        content, sources = smart_field_search(plant_name, field_name, field_description)
        duration = time.time() - start_time
        total_time += duration
        
        print(f"⏱️ Search took {duration:.2f} seconds")
        print(f"📄 Content: {len(content)} chars")
        print(f"🔗 Sources: {len(sources)} URLs")
        print("-" * 50)
    
    print(f"📊 Total time for all field searches: {total_time:.2f} seconds")
    print("✅ Multiple field caching test completed")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing URL Caching System\n")
    
    test1 = test_url_caching()
    test2 = test_cache_expiry()
    test3 = test_multiple_field_caching()
    
    print(f"\n📊 Test Results:")
    print(f"  URL Caching: {'✅' if test1 else '❌'}")
    print(f"  Cache Expiry: {'✅' if test2 else '❌'}")
    print(f"  Multiple Fields: {'✅' if test3 else '❌'}")
    
    if all([test1, test2, test3]):
        print("\n✅ All caching tests passed!")
        print("\n🔧 Benefits:")
        print("  - No repeated scraping of same URLs")
        print("  - Faster processing for subsequent searches")
        print("  - Reduced API calls and bandwidth")
        print("  - 24-hour cache expiry for fresh data")
    else:
        print("\n⚠️ Some caching tests failed")
    
    print("\n💾 Cache location: /Users/<USER>/Downloads/power_plant_project/cache/search_cache/")
