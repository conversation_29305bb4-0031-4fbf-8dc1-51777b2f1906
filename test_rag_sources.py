#!/usr/bin/env python3
"""
Test script to verify RAG method and separate source tracking.
"""

import json
import os
from org_details_desc import ORG_SCHEMA_DESC
from utils import search_google, scrape_text_from_links, build_prompt, call_llm, extract_with_rag, chunk_content_for_rag

def test_separate_source_tracking():
    """Test that sources are saved separately."""
    print("🧪 Testing separate source tracking...")
    
    # Clean up any existing source files
    sources_dir = "/Users/<USER>/Downloads/power_plant_project/workspace/org_sources"
    if os.path.exists(sources_dir):
        import shutil
        shutil.rmtree(sources_dir)
    
    # Test schema
    test_schema = {"organization_name": "string"}
    
    # Get some content
    query = '"Jhajjar Power Plant" organization'
    links = search_google(query, num_results=2)
    
    if links:
        content, sources = scrape_text_from_links(links, max_content_length=3000)
        
        if content and sources:
            prompt = build_prompt("test_org", test_schema, content)
            
            # Call LLM with source tracking
            result = call_llm(prompt, max_tokens=1000, schema=test_schema, sources=sources,
                            plant_name="Jhajjar Power Plant", field_name="organization_name", workspace_type="org")
            
            print("✅ Extraction result:")
            print(json.dumps(result, indent=2))
            
            # Check if sources were saved separately
            sources_file = os.path.join(sources_dir, "org_sources_Jhajjar Power Plant.json")
            if os.path.exists(sources_file):
                with open(sources_file, 'r') as f:
                    saved_sources = json.load(f)
                print(f"✅ Sources saved separately:")
                print(json.dumps(saved_sources, indent=2))
                return True
            else:
                print("❌ Sources file not created")
                return False
    
    return False

def test_rag_chunking():
    """Test RAG chunking functionality."""
    print("\n🧪 Testing RAG chunking...")
    
    # Create a large content string
    large_content = "This is a test content. " * 500  # About 12,000 characters
    
    print(f"📄 Large content size: {len(large_content)} characters")
    
    # Test chunking
    chunks = chunk_content_for_rag(large_content, chunk_size=2000)
    print(f"📚 Split into {len(chunks)} chunks")
    
    for i, chunk in enumerate(chunks):
        print(f"  Chunk {i+1}: {len(chunk)} characters")
    
    return len(chunks) > 1

def test_rag_extraction():
    """Test RAG extraction with large content."""
    print("\n🧪 Testing RAG extraction...")
    
    # Get large content from multiple sources
    query = '"Jhajjar Power Plant" details information'
    links = search_google(query, num_results=5)
    
    if links:
        content, sources = scrape_text_from_links(links, max_content_length=15000)  # Large content
        
        if len(content) > 8000:  # Ensure it's large enough to trigger RAG
            print(f"📄 Large content size: {len(content)} characters")
            
            test_schema = {
                "organization_name": "string",
                "country_name": "string",
                "plant_types": ["array"]
            }
            
            # This should trigger RAG processing
            result = extract_with_rag(content, test_schema, "test_rag", max_chunk_size=4000)
            
            print("✅ RAG extraction result:")
            print(json.dumps(result, indent=2))
            
            # Check if we got meaningful results
            if result.get("organization_name") or result.get("country_name"):
                print("✅ RAG successfully extracted data from large content")
                return True
            else:
                print("⚠️ RAG didn't extract meaningful data")
                return False
        else:
            print(f"⚠️ Content not large enough ({len(content)} chars) to test RAG")
            return False
    
    return False

def test_full_pipeline_with_rag():
    """Test the full pipeline with RAG and separate sources."""
    print("\n🧪 Testing full pipeline with RAG and separate sources...")
    
    # This should use the new RAG method and separate source tracking
    from org_processor import process_org_details
    
    try:
        result = process_org_details("Jhajjar Power Plant")
        
        print("✅ Organization processing result:")
        print(json.dumps(result, indent=2))
        
        # Check if sources were saved separately
        sources_dir = "/Users/<USER>/Downloads/power_plant_project/workspace/org_sources"
        sources_file = os.path.join(sources_dir, "org_sources_Jhajjar Power Plant.json")
        
        if os.path.exists(sources_file):
            with open(sources_file, 'r') as f:
                saved_sources = json.load(f)
            print(f"✅ Sources saved separately with {len(saved_sources)} field(s)")
            return True
        else:
            print("⚠️ Sources not saved separately")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing RAG Method and Separate Source Tracking\n")
    
    test1 = test_separate_source_tracking()
    test2 = test_rag_chunking()
    test3 = test_rag_extraction()
    test4 = test_full_pipeline_with_rag()
    
    print(f"\n📊 Test Results:")
    print(f"  Separate Source Tracking: {'✅' if test1 else '❌'}")
    print(f"  RAG Chunking: {'✅' if test2 else '❌'}")
    print(f"  RAG Extraction: {'✅' if test3 else '❌'}")
    print(f"  Full Pipeline: {'✅' if test4 else '❌'}")
    
    if all([test1, test2, test3, test4]):
        print("\n✅ All RAG and source tracking features working!")
    else:
        print("\n⚠️ Some features need attention")
    
    print("\n🔧 RAG method now handles large content without truncation")
    print("🔧 Sources are saved separately in workspace/*_sources/ directories")
