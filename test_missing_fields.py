#!/usr/bin/env python3
"""
Test script to verify the new multi-level search and missing field processing.
"""

import json
from org_details_desc import ORG_SCHEMA_DESC
from utils import smart_field_search, find_missing_fields, process_missing_fields, extract_keywords_from_description

def test_keyword_extraction():
    """Test keyword extraction from field descriptions."""
    print("🧪 Testing keyword extraction...")
    
    test_descriptions = [
        "The type of CFPP (Central Financial Planning Process) classification",
        "Total auxiliary power consumed by the plant as a percentage of gross generation",
        "The emission factor for CO2 emissions in kg/MWh",
        "The fuel type used in the power plant (coal, gas, biomass, etc.)"
    ]
    
    for desc in test_descriptions:
        keywords = extract_keywords_from_description(desc)
        print(f"Description: {desc}")
        print(f"Keywords: {keywords}")
        print()

def test_missing_field_detection():
    """Test missing field detection."""
    print("🧪 Testing missing field detection...")
    
    # Simulate incomplete organization data
    incomplete_data = {
        "country_name": "India",
        "organization_name": "CLP India Private Limited",
        "plants_count": 1,
        "plant_types": ["Coal"],
        "province": "Haryana",
        # Missing: cfpp_type, currency_in, financial_year, ppa_flag
    }
    
    missing_fields = find_missing_fields(incomplete_data, ORG_SCHEMA_DESC)
    print(f"Found {len(missing_fields)} missing fields:")
    for full_key, field_name, field_schema in missing_fields:
        print(f"  - {field_name}: {field_schema}")
    print()

def test_smart_search():
    """Test the multi-level search strategy."""
    print("🧪 Testing smart search for missing fields...")
    
    plant_name = "Jhajjar Power Plant"
    
    # Test searching for a specific missing field
    field_name = "cfpp_type"
    field_description = "The type of CFPP (Central Financial Planning Process) classification"
    
    print(f"Searching for: {field_name}")
    print(f"Description: {field_description}")
    
    content, sources = smart_field_search(plant_name, field_name, field_description)
    
    print(f"Content length: {len(content)} characters")
    print(f"Sources found: {len(sources)}")
    for i, source in enumerate(sources):
        print(f"  {i+1}. {source}")
    
    if content:
        print(f"Sample content: {content[:200]}...")
    print()

def test_full_missing_field_processing():
    """Test the complete missing field processing pipeline."""
    print("🧪 Testing complete missing field processing...")
    
    plant_name = "Jhajjar Power Plant"
    
    # Simulate incomplete data
    incomplete_data = {
        "country_name": "India",
        "organization_name": "CLP India Private Limited",
        "plants_count": 1,
        "plant_types": ["Coal"],
        "province": "Haryana",
        "_sources": ["https://example.com"]
    }
    
    print("Before processing:")
    print(json.dumps(incomplete_data, indent=2))
    
    # Process missing fields (using a temporary cache path)
    cache_path = "/tmp/test_org_cache.json"
    
    try:
        updated_data = process_missing_fields(plant_name, incomplete_data, ORG_SCHEMA_DESC, cache_path)
        
        print("\nAfter processing:")
        print(json.dumps(updated_data, indent=2))
        
        # Count how many fields were filled
        original_fields = len([k for k, v in incomplete_data.items() if v is not None and k != "_sources"])
        updated_fields = len([k for k, v in updated_data.items() if v is not None and k != "_sources"])
        
        print(f"\nFields filled: {updated_fields - original_fields}")
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")

if __name__ == "__main__":
    print("🚀 Testing Multi-Level Search and Missing Field Processing\n")
    
    test_keyword_extraction()
    test_missing_field_detection()
    test_smart_search()
    test_full_missing_field_processing()
    
    print("✅ All tests completed!")
